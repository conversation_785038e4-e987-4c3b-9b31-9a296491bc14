/**
 * JSON schema for outfit review response using Gemini structured output
 * Based on the reviewOutfitPrompt.ts requirements
 */
import { Type } from "@google/genai";

export const outfitReviewSchema = {
  type: Type.OBJECT,
  properties: {
    reviewFeedback: {
      type: Type.OBJECT,
      description: "Detailed feedback about the user's outfit",
      properties: {
        compliment: {
          type: Type.STRING,
          description: "A genuine and specific positive comment about something that works in the current outfit"
        },
        whatWorks: {
          type: Type.STRING,
          description: "Brief elaboration on 1-2 key strengths of the outfit"
        },
        areasForImprovement: {
          type: Type.STRING,
          description: "Gently point out 1-2 specific areas that could be enhanced"
        },
        quickHack: {
          type: Type.STRING,
          description: "A simple, actionable tip for an immediate improvement"
        },
        improvedVersion: {
          type: Type.STRING,
          description: "Suggest a more comprehensive improvement"
        },
        colorSuggestions: {
          type: Type.STRING,
          description: "Suggest 1-2 colors or color combinations that would complement the user"
        },
        accessoryRecommendations: {
          type: Type.STRING,
          description: "Recommend 1-2 suitable accessories that would enhance the outfit"
        },
        additionalStylingTips: {
          type: Type.STRING,
          description: "Any other relevant styling advice for the specific occasion or outfit type"
        },
        concludingQuote: {
          type: Type.STRING,
          description: "An uplifting and relevant fashion or style quote"
        }
      },
      required: [
        "compliment",
        "whatWorks", 
        "areasForImprovement",
        "quickHack",
        "improvedVersion",
        "colorSuggestions",
        "accessoryRecommendations",
        "additionalStylingTips",
        "concludingQuote"
      ]
    },
    suggestedUserWardrobeAlternatives: {
      type: Type.ARRAY,
      description: "Array of apparelId strings from the user's wardrobe that could improve the outfit",
      items: {
        type: Type.STRING,
        description: "Apparel ID from user's wardrobe"
      }
    },
    suggestedMarketplaceAlternatives: {
      type: Type.ARRAY,
      description: "Array of new apparel item suggestions for marketplace",
      items: {
        type: Type.OBJECT,
        properties: {
          apparelId: {
            type: Type.STRING,
            description: "Always 'new' for marketplace suggestions",
            enum: ["new"]
          },
          apparelType: {
            type: Type.STRING,
            description: "Specific type of the apparel"
          },
          apparelProfile: {
            type: Type.STRING,
            description: "Gender profile of the apparel",
            enum: ["MALE", "FEMALE", "UNISEX"]
          },
          apparelCategory: {
            type: Type.STRING,
            description: "Category of the apparel",
            enum: ["TOPWEAR", "BOTTOMWEAR", "FOOTWEAR", "DRESS", "OUTERWEAR", "ACCESSORY"]
          },
          colour: {
            type: Type.STRING,
            description: "Color of the apparel"
          },
          vibe: {
            type: Type.STRING,
            description: "Vibe of the apparel (e.g., Casual, Formal, Bohemian)"
          },
          pattern: {
            type: Type.STRING,
            description: "Pattern of the apparel (e.g., Solid, Striped, Floral)"
          },
          fabric: {
            type: Type.STRING,
            description: "Fabric or material of the apparel"
          },
          fit: {
            type: Type.STRING,
            description: "Fit of the apparel (e.g., Slim Fit, Relaxed Fit, Oversized)"
          },
          length: {
            type: Type.STRING,
            description: "Length of the apparel if applicable"
          },
          details: {
            type: Type.STRING,
            description: "Brief description or key features of the item"
          }
        },
        required: ["apparelId", "apparelType", "apparelProfile", "apparelCategory", "colour", "vibe", "pattern", "details"]
      }
    }
  },
  required: ["reviewFeedback", "suggestedUserWardrobeAlternatives", "suggestedMarketplaceAlternatives"]
};

/**
 * TypeScript interface for the outfit review response
 */
export interface OutfitReviewResponse {
  reviewFeedback: {
    compliment: string;
    whatWorks: string;
    areasForImprovement: string;
    quickHack: string;
    improvedVersion: string;
    colorSuggestions: string;
    accessoryRecommendations: string;
    additionalStylingTips: string;
    concludingQuote: string;
  };
  suggestedUserWardrobeAlternatives: string[];
  suggestedMarketplaceAlternatives: Array<{
    apparelId: "new";
    apparelType: string;
    apparelProfile: "MALE" | "FEMALE" | "UNISEX";
    apparelCategory: "TOPWEAR" | "BOTTOMWEAR" | "FOOTWEAR" | "DRESS" | "OUTERWEAR" | "ACCESSORY";
    colour: string;
    vibe: string;
    pattern: string;
    fabric?: string;
    fit?: string;
    length?: string;
    details: string;
  }>;
}
